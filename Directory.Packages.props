<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <!-- CLI Framework -->
    <PackageVersion Include="Spectre.Console.Cli" Version="0.50.0" />
    
    <!-- Testing -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="xunit" Version="2.9.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="FluentAssertions" Version="6.12.1" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    
    <!-- Application Framework -->
    <PackageVersion Include="MediatR" Version="12.4.1" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
    
    <!-- File System and XML Processing -->
    <PackageVersion Include="System.IO.Abstractions" Version="21.1.3" />
    <PackageVersion Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>
</Project>
