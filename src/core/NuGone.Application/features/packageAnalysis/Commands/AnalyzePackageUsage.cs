using System.Diagnostics;
using Microsoft.Extensions.Logging;
using NuGone.Application.Shared.Interfaces;
using NuGone.Domain.Features.PackageAnalysis.Entities;
using NuGone.Domain.Shared.ValueObjects;

namespace NuGone.Application.Features.PackageAnalysis.Commands;

/// <summary>
/// Command for analyzing package usage in a solution or project.
/// RFC-0002: Input parameters for unused package detection.
/// </summary>
public partial class AnalyzePackageUsage
{
    public AnalyzePackageUsage(string path)
    {
        Path = path ?? throw new ArgumentNullException(nameof(path));
        ExcludePatterns = new List<string>();
        IncludeTransitiveDependencies = false;
        Verbose = false;
        DryRun = true;
        TimeoutSeconds = 300;
    }

    /// <summary>
    /// Path to the solution file, project file, or directory to analyze.
    /// </summary>
    public string Path { get; }

    /// <summary>
    /// Patterns for files/folders to exclude from analysis.
    /// RFC-0002: User-defined exclusion patterns.
    /// </summary>
    public IList<string> ExcludePatterns { get; }

    /// <summary>
    /// Whether to include transitive dependencies in the analysis.
    /// RFC-0002: Transitive dependency analysis option.
    /// </summary>
    public bool IncludeTransitiveDependencies { get; set; }

    /// <summary>
    /// Whether to enable verbose output with detailed information.
    /// </summary>
    public bool Verbose { get; set; }

    /// <summary>
    /// Optional target framework to filter analysis.
    /// If not specified, all target frameworks are analyzed.
    /// </summary>
    public string? TargetFramework { get; set; }

    /// <summary>
    /// Whether to perform a dry run without making any changes.
    /// </summary>
    public bool DryRun { get; set; }

    /// <summary>
    /// Timeout for the analysis operation in seconds.
    /// Default is 300 seconds (5 minutes).
    /// </summary>
    public int TimeoutSeconds { get; set; }

    /// <summary>
    /// Adds an exclusion pattern.
    /// </summary>
    /// <param name="pattern">The pattern to exclude</param>
    public void AddExcludePattern(string pattern)
    {
        if (!string.IsNullOrWhiteSpace(pattern) && !ExcludePatterns.Contains(pattern))
            ExcludePatterns.Add(pattern);
    }

    /// <summary>
    /// Adds multiple exclusion patterns.
    /// </summary>
    /// <param name="patterns">The patterns to exclude</param>
    public void AddExcludePatterns(IEnumerable<string> patterns)
    {
        foreach (var pattern in patterns)
            AddExcludePattern(pattern);
    }

    /// <summary>
    /// Result for package usage analysis command.
    /// RFC-0002: Output data for unused package detection results.
    /// </summary>
    public class Result
    {
        public Result(
            string analyzedPath,
            TimeSpan analysisTime,
            IEnumerable<ProjectAnalysisResult> projectResults
        )
        {
            AnalyzedPath = analyzedPath ?? throw new ArgumentNullException(nameof(analyzedPath));
            AnalysisTime = analysisTime;
            ProjectResults =
                projectResults?.ToList() ?? throw new ArgumentNullException(nameof(projectResults));

            // Calculate summary statistics
            TotalProjects = ProjectResults.Count;
            TotalPackages = ProjectResults.Sum(p => p.TotalPackages);
            UnusedPackages = ProjectResults.Sum(p => p.UnusedPackages);
            UsedPackages = ProjectResults.Sum(p => p.UsedPackages);
        }

        /// <summary>
        /// The path that was analyzed (solution, project, or directory).
        /// </summary>
        public string AnalyzedPath { get; }

        /// <summary>
        /// Time taken to complete the analysis.
        /// </summary>
        public TimeSpan AnalysisTime { get; }

        /// <summary>
        /// Results for each project that was analyzed.
        /// </summary>
        public IList<ProjectAnalysisResult> ProjectResults { get; }

        /// <summary>
        /// Total number of projects analyzed.
        /// </summary>
        public int TotalProjects { get; }

        /// <summary>
        /// Total number of packages across all projects.
        /// </summary>
        public int TotalPackages { get; }

        /// <summary>
        /// Total number of unused packages across all projects.
        /// </summary>
        public int UnusedPackages { get; }

        /// <summary>
        /// Total number of used packages across all projects.
        /// </summary>
        public int UsedPackages { get; }

        /// <summary>
        /// Percentage of packages that are unused.
        /// </summary>
        public double UnusedPercentage =>
            TotalPackages > 0 ? (double)UnusedPackages / TotalPackages * 100 : 0;

        /// <summary>
        /// Gets all unused package details across all projects.
        /// </summary>
        /// <returns>Collection of unused package details</returns>
        public IEnumerable<PackageUsageDetail> GetAllUnusedPackages()
        {
            return ProjectResults.SelectMany(p => p.UnusedPackageDetails);
        }

        /// <summary>
        /// Gets all used package details across all projects.
        /// </summary>
        /// <returns>Collection of used package details</returns>
        public IEnumerable<PackageUsageDetail> GetAllUsedPackages()
        {
            return ProjectResults.SelectMany(p => p.UsedPackageDetails);
        }

        /// <summary>
        /// Gets unused packages grouped by package ID.
        /// Useful for identifying packages that are unused across multiple projects.
        /// </summary>
        /// <returns>Dictionary with package ID as key and list of usage details as value</returns>
        public Dictionary<string, List<PackageUsageDetail>> GetUnusedPackagesGroupedById()
        {
            return GetAllUnusedPackages()
                .GroupBy(p => p.PackageId, StringComparer.OrdinalIgnoreCase)
                .ToDictionary(g => g.Key, g => g.ToList(), StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if the analysis found any unused packages.
        /// </summary>
        /// <returns>True if there are unused packages, false otherwise</returns>
        public bool HasUnusedPackages() => UnusedPackages > 0;

        /// <summary>
        /// Gets a summary string of the analysis results.
        /// </summary>
        /// <returns>Summary string</returns>
        public string GetSummary()
        {
            return $"Analyzed {TotalProjects} project(s) with {TotalPackages} package(s). "
                + $"Found {UnusedPackages} unused package(s) ({UnusedPercentage:F1}%) and {UsedPackages} used package(s).";
        }
    }

    /// <summary>
    /// Analysis result for a single project.
    /// </summary>
    public class ProjectAnalysisResult
    {
        public ProjectAnalysisResult(
            string projectName,
            string projectPath,
            string targetFramework,
            IEnumerable<PackageUsageDetail> unusedPackageDetails,
            IEnumerable<PackageUsageDetail> usedPackageDetails
        )
        {
            ProjectName = projectName ?? throw new ArgumentNullException(nameof(projectName));
            ProjectPath = projectPath ?? throw new ArgumentNullException(nameof(projectPath));
            TargetFramework =
                targetFramework ?? throw new ArgumentNullException(nameof(targetFramework));
            UnusedPackageDetails =
                unusedPackageDetails?.ToList()
                ?? throw new ArgumentNullException(nameof(unusedPackageDetails));
            UsedPackageDetails =
                usedPackageDetails?.ToList()
                ?? throw new ArgumentNullException(nameof(usedPackageDetails));

            UnusedPackages = UnusedPackageDetails.Count;
            UsedPackages = UsedPackageDetails.Count;
            TotalPackages = UnusedPackages + UsedPackages;
        }

        /// <summary>
        /// Name of the project.
        /// </summary>
        public string ProjectName { get; }

        /// <summary>
        /// Full path to the project file.
        /// </summary>
        public string ProjectPath { get; }

        /// <summary>
        /// Target framework of the project.
        /// </summary>
        public string TargetFramework { get; }

        /// <summary>
        /// Details of unused packages in this project.
        /// </summary>
        public IList<PackageUsageDetail> UnusedPackageDetails { get; }

        /// <summary>
        /// Details of used packages in this project.
        /// </summary>
        public IList<PackageUsageDetail> UsedPackageDetails { get; }

        /// <summary>
        /// Number of unused packages in this project.
        /// </summary>
        public int UnusedPackages { get; }

        /// <summary>
        /// Number of used packages in this project.
        /// </summary>
        public int UsedPackages { get; }

        /// <summary>
        /// Total number of packages in this project.
        /// </summary>
        public int TotalPackages { get; }

        /// <summary>
        /// Percentage of packages that are unused in this project.
        /// </summary>
        public double UnusedPercentage =>
            TotalPackages > 0 ? (double)UnusedPackages / TotalPackages * 100 : 0;
    }

    /// <summary>
    /// Detailed information about a package's usage.
    /// </summary>
    public class PackageUsageDetail
    {
        public PackageUsageDetail(
            string packageId,
            string version,
            bool isDirect,
            bool isUsed,
            string? condition = null,
            IEnumerable<string>? usageLocations = null,
            IEnumerable<string>? detectedNamespaces = null
        )
        {
            PackageId = packageId ?? throw new ArgumentNullException(nameof(packageId));
            Version = version ?? throw new ArgumentNullException(nameof(version));
            IsDirect = isDirect;
            IsUsed = isUsed;
            Condition = condition;
            UsageLocations = usageLocations?.ToList() ?? new List<string>();
            DetectedNamespaces = detectedNamespaces?.ToList() ?? new List<string>();
        }

        /// <summary>
        /// The package identifier.
        /// </summary>
        public string PackageId { get; }

        /// <summary>
        /// The package version.
        /// </summary>
        public string Version { get; }

        /// <summary>
        /// Whether this is a direct dependency.
        /// </summary>
        public bool IsDirect { get; }

        /// <summary>
        /// Whether the package is used in the codebase.
        /// </summary>
        public bool IsUsed { get; }

        /// <summary>
        /// Optional condition from the PackageReference.
        /// </summary>
        public string? Condition { get; }

        /// <summary>
        /// File paths where usage was detected.
        /// </summary>
        public IList<string> UsageLocations { get; }

        /// <summary>
        /// Namespaces from this package that were detected.
        /// </summary>
        public IList<string> DetectedNamespaces { get; }

        /// <summary>
        /// Gets a display string for the package.
        /// </summary>
        /// <returns>Display string</returns>
        public string GetDisplayString()
        {
            var dependencyType = IsDirect ? "Direct" : "Transitive";
            var usageStatus = IsUsed ? "Used" : "Unused";
            var conditionText = !string.IsNullOrWhiteSpace(Condition)
                ? $" (Condition: {Condition})"
                : "";

            return $"{PackageId} {Version} ({dependencyType}, {usageStatus}){conditionText}";
        }
    }

    /// <summary>
    /// Command handler for analyzing package usage in a solution or project.
    /// Implements the core algorithm specified in RFC-0002.
    /// </summary>
    public class Handler
    {
        private readonly ISolutionRepository _solutionRepository;
        private readonly IProjectRepository _projectRepository;
        private readonly INuGetRepository _nugetRepository;
        private readonly IPackageUsageAnalyzer _packageUsageAnalyzer;
        private readonly ILogger<Handler> _logger;

        public Handler(
            ISolutionRepository solutionRepository,
            IProjectRepository projectRepository,
            INuGetRepository nugetRepository,
            IPackageUsageAnalyzer packageUsageAnalyzer,
            ILogger<Handler> logger
        )
        {
            _solutionRepository =
                solutionRepository ?? throw new ArgumentNullException(nameof(solutionRepository));
            _projectRepository =
                projectRepository ?? throw new ArgumentNullException(nameof(projectRepository));
            _nugetRepository =
                nugetRepository ?? throw new ArgumentNullException(nameof(nugetRepository));
            _packageUsageAnalyzer =
                packageUsageAnalyzer
                ?? throw new ArgumentNullException(nameof(packageUsageAnalyzer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the package usage analysis command.
        /// RFC-0002: Main entry point for unused package detection.
        /// </summary>
        /// <param name="command">The analysis command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Analysis result containing unused packages</returns>
        public async Task<Domain.Shared.ValueObjects.Result<Result>> HandleAsync(
            AnalyzePackageUsage command,
            CancellationToken cancellationToken = default
        )
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation(
                    "Starting package usage analysis for path: {Path}",
                    command.Path
                );

                // Step 1: Validate input
                var validationResult = ValidateCommand(command);
                if (validationResult.IsFailure)
                    return Domain.Shared.ValueObjects.Result<Result>.Failure(
                        validationResult.Error
                    );

                // Step 2: Determine if we're analyzing a solution or individual project
                var analysisTarget = await DetermineAnalysisTargetAsync(
                    command.Path,
                    cancellationToken
                );
                if (analysisTarget.IsFailure)
                    return Domain.Shared.ValueObjects.Result<Result>.Failure(analysisTarget.Error);

                // Step 3: Load the solution or project
                var loadResult = await LoadAnalysisTargetAsync(
                    analysisTarget.Value,
                    command.Path,
                    cancellationToken
                );
                if (loadResult.IsFailure)
                    return Domain.Shared.ValueObjects.Result<Result>.Failure(loadResult.Error);

                // Step 4: Apply exclude patterns to projects
                var solution = loadResult.Value;
                ApplyExcludePatterns(solution, command.ExcludePatterns);

                // Step 5: Load package references for all projects
                var packageLoadResult = await LoadPackageReferencesAsync(
                    solution,
                    cancellationToken
                );
                if (packageLoadResult.IsFailure)
                    return Domain.Shared.ValueObjects.Result<Result>.Failure(
                        packageLoadResult.Error
                    );

                // Step 6: Perform the analysis
                var analysisResult = await PerformAnalysisAsync(
                    solution,
                    command,
                    cancellationToken
                );
                if (analysisResult.IsFailure)
                    return Domain.Shared.ValueObjects.Result<Result>.Failure(analysisResult.Error);

                stopwatch.Stop();
                _logger.LogInformation(
                    "Package usage analysis completed in {ElapsedTime}",
                    stopwatch.Elapsed
                );

                var result = new Result(command.Path, stopwatch.Elapsed, analysisResult.Value);

                return Domain.Shared.ValueObjects.Result<Result>.Success(result);
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Package usage analysis was cancelled");
                return Domain.Shared.ValueObjects.Result<Result>.Failure(
                    "OPERATION_CANCELLED",
                    "Analysis was cancelled"
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred during package usage analysis");
                return Domain.Shared.ValueObjects.Result<Result>.Failure(
                    "UNEXPECTED_ERROR",
                    $"An unexpected error occurred: {ex.Message}"
                );
            }
        }

        private static Domain.Shared.ValueObjects.Result ValidateCommand(
            AnalyzePackageUsage command
        )
        {
            if (command == null)
                return Domain.Shared.ValueObjects.Result.Failure(
                    "INVALID_COMMAND",
                    "Command cannot be null"
                );

            if (string.IsNullOrWhiteSpace(command.Path))
                return Domain.Shared.ValueObjects.Result.Failure(
                    "INVALID_PATH",
                    "Path cannot be null or empty"
                );

            return Domain.Shared.ValueObjects.Result.Success();
        }

        private async Task<Domain.Shared.ValueObjects.Result<AnalysisTargetType>> DetermineAnalysisTargetAsync(
            string path,
            CancellationToken cancellationToken
        )
        {
            if (!await _projectRepository.ExistsAsync(path))
                return Domain.Shared.ValueObjects.Result<AnalysisTargetType>.Failure(
                    "PATH_NOT_FOUND",
                    $"Path does not exist: {path}"
                );

            // Check if it's a solution file
            if (
                path.EndsWith(".sln", StringComparison.OrdinalIgnoreCase)
                || path.EndsWith(".slnx", StringComparison.OrdinalIgnoreCase)
            )
            {
                return Domain.Shared.ValueObjects.Result<AnalysisTargetType>.Success(
                    AnalysisTargetType.Solution
                );
            }

            // Check if it's a project file
            if (
                path.EndsWith(".csproj", StringComparison.OrdinalIgnoreCase)
                || path.EndsWith(".vbproj", StringComparison.OrdinalIgnoreCase)
                || path.EndsWith(".fsproj", StringComparison.OrdinalIgnoreCase)
            )
            {
                return Domain.Shared.ValueObjects.Result<AnalysisTargetType>.Success(
                    AnalysisTargetType.Project
                );
            }

            // Check if it's a directory - look for solution files first, then projects
            var solutionFiles = await _solutionRepository.DiscoverSolutionFilesAsync(
                path,
                cancellationToken
            );
            if (solutionFiles.Any())
            {
                return Domain.Shared.ValueObjects.Result<AnalysisTargetType>.Success(
                    AnalysisTargetType.Directory
                );
            }

            var projectFiles = await _projectRepository.DiscoverProjectFilesAsync(
                path,
                cancellationToken
            );
            if (projectFiles.Any())
            {
                return Domain.Shared.ValueObjects.Result<AnalysisTargetType>.Success(
                    AnalysisTargetType.Directory
                );
            }

            return Domain.Shared.ValueObjects.Result<AnalysisTargetType>.Failure(
                "NO_PROJECTS_FOUND",
                "No solution or project files found in the specified path"
            );
        }

        private async Task<Domain.Shared.ValueObjects.Result<Solution>> LoadAnalysisTargetAsync(
            AnalysisTargetType targetType,
            string path,
            CancellationToken cancellationToken
        )
        {
            switch (targetType)
            {
                case AnalysisTargetType.Solution:
                    return await LoadSolutionAsync(path, cancellationToken);

                case AnalysisTargetType.Project:
                    return await LoadSingleProjectAsSolutionAsync(path, cancellationToken);

                case AnalysisTargetType.Directory:
                    return await LoadDirectoryAsSolutionAsync(path, cancellationToken);

                default:
                    return Domain.Shared.ValueObjects.Result<Solution>.Failure(
                        "INVALID_TARGET_TYPE",
                        $"Unknown analysis target type: {targetType}"
                    );
            }
        }

        private async Task<Domain.Shared.ValueObjects.Result<Solution>> LoadSolutionAsync(
            string solutionPath,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var solution = await _solutionRepository.LoadSolutionAsync(
                    solutionPath,
                    cancellationToken
                );

                // Load each project in the solution
                var loadedProjects = new List<Project>();
                foreach (var project in solution.Projects)
                {
                    var loadedProject = await _projectRepository.LoadProjectAsync(
                        project.FilePath,
                        cancellationToken
                    );
                    loadedProjects.Add(loadedProject);
                }

                // Replace projects with loaded versions
                solution.Projects.Clear();
                foreach (var project in loadedProjects)
                {
                    solution.AddProject(project);
                }

                return Domain.Shared.ValueObjects.Result<Solution>.Success(solution);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading solution: {SolutionPath}", solutionPath);
                return Domain.Shared.ValueObjects.Result<Solution>.Failure(
                    "SOLUTION_LOAD_ERROR",
                    $"Failed to load solution: {ex.Message}"
                );
            }
        }

        private async Task<Domain.Shared.ValueObjects.Result<Solution>> LoadSingleProjectAsSolutionAsync(
            string projectPath,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var project = await _projectRepository.LoadProjectAsync(
                    projectPath,
                    cancellationToken
                );
                var solutionName = Path.GetFileNameWithoutExtension(projectPath) + "_Solution";
                var solutionPath = Path.ChangeExtension(projectPath, ".sln");

                var solution = new Solution(solutionPath, solutionName);
                solution.AddProject(project);

                return Domain.Shared.ValueObjects.Result<Solution>.Success(solution);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error loading project as solution: {ProjectPath}",
                    projectPath
                );
                return Domain.Shared.ValueObjects.Result<Solution>.Failure(
                    "PROJECT_LOAD_ERROR",
                    $"Failed to load project: {ex.Message}"
                );
            }
        }

        private async Task<Domain.Shared.ValueObjects.Result<Solution>> LoadDirectoryAsSolutionAsync(
            string directoryPath,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // First try to find a solution file
                var solutionFiles = await _solutionRepository.DiscoverSolutionFilesAsync(
                    directoryPath,
                    cancellationToken
                );
                if (solutionFiles.Any())
                {
                    return await LoadSolutionAsync(solutionFiles.First(), cancellationToken);
                }

                // If no solution file, create a virtual solution from all projects
                var projectFiles = await _projectRepository.DiscoverProjectFilesAsync(
                    directoryPath,
                    cancellationToken
                );
                if (!projectFiles.Any())
                {
                    return Domain.Shared.ValueObjects.Result<Solution>.Failure(
                        "NO_PROJECTS_FOUND",
                        $"No projects found in directory: {directoryPath}"
                    );
                }

                var solutionName = Path.GetFileName(directoryPath) + "_Solution";
                var solutionPath = Path.Combine(directoryPath, solutionName + ".sln");
                var solution = new Solution(solutionPath, solutionName);

                foreach (var projectFile in projectFiles)
                {
                    var project = await _projectRepository.LoadProjectAsync(
                        projectFile,
                        cancellationToken
                    );
                    solution.AddProject(project);
                }

                return Domain.Shared.ValueObjects.Result<Solution>.Success(solution);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error loading directory as solution: {DirectoryPath}",
                    directoryPath
                );
                return Domain.Shared.ValueObjects.Result<Solution>.Failure(
                    "DIRECTORY_LOAD_ERROR",
                    $"Failed to load directory: {ex.Message}"
                );
            }
        }

        private static void ApplyExcludePatterns(
            Solution solution,
            IEnumerable<string> excludePatterns
        )
        {
            foreach (var project in solution.Projects)
            {
                foreach (var pattern in excludePatterns)
                {
                    project.AddExcludePattern(pattern);
                }
            }
        }

        private async Task<Domain.Shared.ValueObjects.Result> LoadPackageReferencesAsync(
            Solution solution,
            CancellationToken cancellationToken
        )
        {
            try
            {
                foreach (var project in solution.Projects)
                {
                    var packageReferences = await _nugetRepository.ExtractPackageReferencesAsync(
                        project.FilePath,
                        cancellationToken
                    );

                    foreach (var packageRef in packageReferences)
                    {
                        project.AddPackageReference(packageRef);
                    }
                }

                return Domain.Shared.ValueObjects.Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading package references");
                return Domain.Shared.ValueObjects.Result.Failure(
                    "PACKAGE_LOAD_ERROR",
                    $"Failed to load package references: {ex.Message}"
                );
            }
        }

        private async Task<Domain.Shared.ValueObjects.Result<
            IEnumerable<ProjectAnalysisResult>
        >> PerformAnalysisAsync(
            Solution solution,
            AnalyzePackageUsage command,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Validate inputs
                var validationResult = await _packageUsageAnalyzer.ValidateInputsAsync(solution);
                if (!validationResult.IsValid)
                {
                    var errors = string.Join(", ", validationResult.Errors);
                    return Domain.Shared.ValueObjects.Result<
                        IEnumerable<ProjectAnalysisResult>
                    >.Failure("VALIDATION_ERROR", $"Validation failed: {errors}");
                }

                // Perform the analysis
                await _packageUsageAnalyzer.AnalyzePackageUsageAsync(solution, cancellationToken);

                // Convert results to DTOs
                var projectResults = new List<ProjectAnalysisResult>();
                foreach (var project in solution.Projects)
                {
                    var unusedPackages = project
                        .GetUnusedPackages()
                        .Select(ConvertToPackageUsageDetail);
                    var usedPackages = project
                        .GetUsedPackages()
                        .Select(ConvertToPackageUsageDetail);

                    var projectResult = new ProjectAnalysisResult(
                        project.Name,
                        project.FilePath,
                        project.TargetFramework,
                        unusedPackages,
                        usedPackages
                    );

                    projectResults.Add(projectResult);
                }

                return Domain.Shared.ValueObjects.Result<
                    IEnumerable<ProjectAnalysisResult>
                >.Success(projectResults);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing package analysis");
                return Domain.Shared.ValueObjects.Result<
                    IEnumerable<ProjectAnalysisResult>
                >.Failure("ANALYSIS_ERROR", $"Analysis failed: {ex.Message}");
            }
        }

        private static PackageUsageDetail ConvertToPackageUsageDetail(PackageReference packageRef)
        {
            return new PackageUsageDetail(
                packageRef.PackageId,
                packageRef.Version,
                packageRef.IsDirect,
                packageRef.IsUsed,
                packageRef.Condition,
                packageRef.UsageLocations,
                packageRef.DetectedNamespaces
            );
        }

        private enum AnalysisTargetType
        {
            Solution,
            Project,
            Directory,
        }
    }
}
