{"analyzedPath": "/home/<USER>/Code/ahmet-cetinkaya/nugone/test-projects/sample-project/SampleProject.csproj", "analysisTime": {"totalSeconds": 0.0437709, "formatted": "0.04s"}, "summary": {"totalProjects": 1, "totalPackages": 4, "unusedPackages": 1, "usedPackages": 3, "unusedPercentage": 25}, "projects": [{"projectName": "SampleProject", "projectPath": "/home/<USER>/Code/ahmet-cetinkaya/nugone/test-projects/sample-project/SampleProject.csproj", "targetFramework": "net9.0", "packageCounts": {"total": 4, "used": 3, "unused": 1, "unusedPercentage": 25}, "unusedPackages": [{"packageId": "FluentAssertions", "version": "6.12.0", "isDirect": true, "condition": null, "usageLocations": [], "detectedNamespaces": []}], "usedPackages": [{"packageId": "System.Text.Json", "version": "9.0.0", "isDirect": true, "condition": null, "usageLocations": ["/home/<USER>/Code/ahmet-cetinkaya/nugone/test-projects/sample-project/Program.cs"], "detectedNamespaces": ["System.Text.Json", "System.Text"]}, {"packageId": "Newtonsoft.Json", "version": "13.0.3", "isDirect": true, "condition": null, "usageLocations": ["/home/<USER>/Code/ahmet-cetinkaya/nugone/test-projects/sample-project/Program.cs"], "detectedNamespaces": ["Newtonsoft"]}, {"packageId": "Microsoft.Extensions.Logging", "version": "9.0.0", "isDirect": true, "condition": null, "usageLocations": ["/home/<USER>/Code/ahmet-cetinkaya/nugone/test-projects/sample-project/Program.cs"], "detectedNamespaces": ["Microsoft.Extensions"]}]}]}